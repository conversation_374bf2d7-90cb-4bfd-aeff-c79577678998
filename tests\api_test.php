<?php
/**
 * API接口测试脚本
 * 用于测试所有API接口是否正常工作
 */

class ApiTest
{
    private $baseUrl;
    private $apiKey;
    
    public function __construct()
    {
        $this->baseUrl = 'http://localhost'; // 修改为您的域名
        $this->apiKey = 'poster_api_key_2025_secure_token_12345';
    }
    
    /**
     * 运行API测试
     */
    public function runApiTests()
    {
        echo "=== API接口测试开始 ===\n\n";
        
        $tests = [
            'testExternalHealthApi' => '外部健康检查API',
            'testExternalParameterDataApi' => '外部参数数据API',
            'testExternalParameterConfigApi' => '外部参数配置API',
            'testInternalParseTemplateApi' => '内部模板解析API',
            'testInternalConfigManagementApi' => '内部配置管理API',
            'testInternalUserDataApi' => '内部用户数据API',
        ];
        
        $passed = 0;
        $failed = 0;
        
        foreach ($tests as $method => $description) {
            echo "测试: {$description}\n";
            try {
                $result = $this->$method();
                if ($result) {
                    echo "✅ 通过\n\n";
                    $passed++;
                } else {
                    echo "❌ 失败\n\n";
                    $failed++;
                }
            } catch (Exception $e) {
                echo "❌ 异常: " . $e->getMessage() . "\n\n";
                $failed++;
            }
        }
        
        echo "=== API测试结果 ===\n";
        echo "通过: {$passed}\n";
        echo "失败: {$failed}\n";
        echo "总计: " . ($passed + $failed) . "\n";
    }
    
    /**
     * 测试外部健康检查API
     */
    public function testExternalHealthApi()
    {
        $url = $this->baseUrl . '/api/external/health';
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $response = $this->makeRequest('GET', $url, null, $headers);
        
        if ($response && $response['code'] === 200) {
            echo "响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
            return true;
        }
        
        echo "健康检查API失败\n";
        return false;
    }
    
    /**
     * 测试外部参数数据API
     */
    public function testExternalParameterDataApi()
    {
        // 首先创建测试数据
        $testDataId = $this->createTestUserData();
        if (!$testDataId) {
            echo "无法创建测试数据\n";
            return false;
        }
        
        $url = $this->baseUrl . "/api/external/parameter-data/{$testDataId}";
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $response = $this->makeRequest('GET', $url, null, $headers);
        
        // 清理测试数据
        $this->cleanupTestData($testDataId);
        
        if ($response && $response['code'] === 200) {
            echo "参数数据获取成功\n";
            return true;
        }
        
        echo "参数数据API失败\n";
        return false;
    }
    
    /**
     * 测试外部参数配置API
     */
    public function testExternalParameterConfigApi()
    {
        // 首先创建测试配置
        $testConfigId = $this->createTestConfig();
        if (!$testConfigId) {
            echo "无法创建测试配置\n";
            return false;
        }
        
        $url = $this->baseUrl . "/api/external/parameter-config/{$testConfigId}";
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $response = $this->makeRequest('GET', $url, null, $headers);
        
        // 清理测试数据
        $this->cleanupTestConfig($testConfigId);
        
        if ($response && $response['code'] === 200) {
            echo "参数配置获取成功\n";
            return true;
        }
        
        echo "参数配置API失败\n";
        return false;
    }
    
    /**
     * 测试内部模板解析API
     */
    public function testInternalParseTemplateApi()
    {
        $url = $this->baseUrl . '/api/poster/parse-template';
        $data = [
            'template_id' => '2'
        ];
        
        $response = $this->makeRequest('POST', $url, $data);
        
        if ($response && $response['code'] === 200) {
            echo "模板解析成功\n";
            return true;
        }
        
        echo "模板解析API失败\n";
        return false;
    }
    
    /**
     * 测试内部配置管理API
     */
    public function testInternalConfigManagementApi()
    {
        // 测试创建配置
        $url = $this->baseUrl . '/api/poster/create-config';
        $data = [
            'template_id' => '2',
            'config_name' => 'API测试配置',
            'config_description' => '这是API测试创建的配置',
            'selected_parameters' => ['param-1'],
            'parameter_labels' => ['param-1' => '测试参数'],
            'parameter_required' => ['param-1' => true],
        ];
        
        $response = $this->makeRequest('POST', $url, $data);
        
        if (!$response || $response['code'] !== 200) {
            echo "配置创建API失败\n";
            return false;
        }
        
        $configId = $response['data']['config_id'] ?? null;
        if (!$configId) {
            echo "配置创建成功但未返回配置ID\n";
            return false;
        }
        
        echo "配置创建成功，ID: {$configId}\n";
        
        // 测试获取配置列表
        $listUrl = $this->baseUrl . '/api/poster/config-list';
        $listResponse = $this->makeRequest('GET', $listUrl);
        
        if (!$listResponse || $listResponse['code'] !== 200) {
            echo "配置列表获取失败\n";
            return false;
        }
        
        echo "配置列表获取成功\n";
        
        // 清理测试数据
        $this->cleanupTestConfig($configId);
        
        return true;
    }
    
    /**
     * 测试内部用户数据API
     */
    public function testInternalUserDataApi()
    {
        // 先创建配置
        $configId = $this->createTestConfig();
        if (!$configId) {
            echo "无法创建测试配置\n";
            return false;
        }
        
        // 测试提交用户数据
        $url = $this->baseUrl . '/api/poster/submit-data';
        $data = [
            'config_id' => $configId,
            'parameter_values' => [
                'test_param' => '测试参数值'
            ],
            'is_draft' => 1,
        ];
        
        $response = $this->makeRequest('POST', $url, $data);
        
        if (!$response || $response['code'] !== 200) {
            echo "用户数据提交失败\n";
            $this->cleanupTestConfig($configId);
            return false;
        }
        
        $dataId = $response['data']['data_id'] ?? null;
        if (!$dataId) {
            echo "用户数据提交成功但未返回数据ID\n";
            $this->cleanupTestConfig($configId);
            return false;
        }
        
        echo "用户数据提交成功，ID: {$dataId}\n";
        
        // 测试获取用户数据列表
        $listUrl = $this->baseUrl . '/api/poster/user-data-list';
        $listResponse = $this->makeRequest('GET', $listUrl);
        
        if (!$listResponse || $listResponse['code'] !== 200) {
            echo "用户数据列表获取失败\n";
            return false;
        }
        
        echo "用户数据列表获取成功\n";
        
        // 清理测试数据
        $this->cleanupTestData($dataId);
        $this->cleanupTestConfig($configId);
        
        return true;
    }
    
    /**
     * 创建测试配置
     */
    private function createTestConfig()
    {
        try {
            $configData = [
                'template_id' => '2',
                'config_name' => 'API测试配置_' . time(),
                'parameters' => [
                    [
                        'id' => 'param-1',
                        'elementUuid' => 'test-uuid-1',
                        'parameterName' => 'test_param',
                        'parameterLabel' => '测试参数',
                        'parameterType' => 'text',
                        'isRequired' => true,
                        'isEnabled' => true,
                        'displayOrder' => 1,
                    ]
                ],
            ];
            
            $config = \app\common\model\PosterTemplateConfig::createConfig($configData);
            return $config ? $config->id : null;
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * 创建测试用户数据
     */
    private function createTestUserData()
    {
        try {
            $configId = $this->createTestConfig();
            if (!$configId) {
                return null;
            }
            
            $userData = [
                'config_id' => $configId,
                'template_id' => '2',
                'parameter_values' => [
                    'test_param' => '测试参数值'
                ],
            ];
            
            $userDataModel = \app\common\model\PosterUserData::createData($userData);
            return $userDataModel ? $userDataModel->id : null;
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * 清理测试配置
     */
    private function cleanupTestConfig($configId)
    {
        try {
            $config = \app\common\model\PosterTemplateConfig::find($configId);
            if ($config) {
                $config->delete();
            }
        } catch (Exception $e) {
            // 忽略清理错误
        }
    }
    
    /**
     * 清理测试数据
     */
    private function cleanupTestData($dataId)
    {
        try {
            $userData = \app\common\model\PosterUserData::find($dataId);
            if ($userData) {
                $configId = $userData->config_id;
                $userData->delete();
                $this->cleanupTestConfig($configId);
            }
        } catch (Exception $e) {
            // 忽略清理错误
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private function makeRequest($method, $url, $data = null, $headers = [])
    {
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $headers = array_merge($defaultHeaders, $headers);
        
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false,
        ]);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "CURL错误: {$error}\n";
            return null;
        }
        
        if ($response === false) {
            echo "请求失败\n";
            return null;
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "JSON解析失败: " . json_last_error_msg() . "\n";
            echo "原始响应: {$response}\n";
            return null;
        }
        
        return $result;
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    // 引入ThinkPHP框架
    require_once __DIR__ . '/../thinkphp/start.php';
    
    $test = new ApiTest();
    $test->runApiTests();
} else {
    echo "请在命令行中运行此测试脚本\n";
}
