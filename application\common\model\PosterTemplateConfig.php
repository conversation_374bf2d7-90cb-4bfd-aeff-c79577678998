<?php

namespace app\common\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * 海报模板参数配置模型
 * Class PosterTemplateConfig
 * @package app\common\model
 */
class PosterTemplateConfig extends Model
{
    use SoftDelete;

    protected $name = 'poster_template_configs';
    protected $pk = 'id';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';

    // JSON字段
    protected $json = ['parameters'];
    protected $jsonAssoc = true;

    /**
     * 状态常量
     */
    const STATUS_DISABLED = 0; // 禁用
    const STATUS_ENABLED = 1;  // 启用

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用',
        ];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 参数配置获取器 - 确保返回数组格式
     * @param $value
     * @return array
     */
    public function getParametersAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * 参数配置设置器 - 确保存储为JSON格式
     * @param $value
     * @return string
     */
    public function setParametersAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 创建时间获取器
     * @param $value
     * @return string
     */
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 更新时间获取器
     * @param $value
     * @return string
     */
    public function getUpdateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 关联用户数据
     * @return \think\model\relation\HasMany
     */
    public function userData()
    {
        return $this->hasMany(PosterUserData::class, 'config_id', 'id');
    }

    /**
     * 获取启用的配置列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public static function getEnabledList($where = [], $page = 1, $limit = 20)
    {
        $query = self::where('status', self::STATUS_ENABLED);
        
        if (!empty($where['template_id'])) {
            $query->where('template_id', $where['template_id']);
        }
        
        if (!empty($where['created_by'])) {
            $query->where('created_by', $where['created_by']);
        }
        
        if (!empty($where['keyword'])) {
            $query->where(function($q) use ($where) {
                $q->whereLike('config_name', '%' . $where['keyword'] . '%')
                  ->whereOr('template_title', '%' . $where['keyword'] . '%');
            });
        }

        $total = $query->count();
        $list = $query->order('create_time desc')
                     ->page($page, $limit)
                     ->select()
                     ->toArray();

        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit,
        ];
    }

    /**
     * 根据模板ID获取配置
     * @param string $templateId
     * @return array
     */
    public static function getByTemplateId($templateId)
    {
        return self::where('template_id', $templateId)
                  ->where('status', self::STATUS_ENABLED)
                  ->order('create_time desc')
                  ->select()
                  ->toArray();
    }

    /**
     * 创建配置
     * @param array $data
     * @return PosterTemplateConfig|false
     */
    public static function createConfig($data)
    {
        $config = new self();
        $config->id = $data['id'] ?? self::generateId();
        $config->template_id = $data['template_id'];
        $config->template_title = $data['template_title'] ?? '';
        $config->config_name = $data['config_name'];
        $config->config_description = $data['config_description'] ?? '';
        $config->parameters = $data['parameters'];
        $config->created_by = $data['created_by'] ?? 0;
        $config->status = $data['status'] ?? self::STATUS_ENABLED;
        
        return $config->save() ? $config : false;
    }

    /**
     * 生成唯一ID
     * @return string
     */
    public static function generateId()
    {
        return 'config_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }

    /**
     * 验证参数配置格式
     * @param array $parameters
     * @return bool
     */
    public static function validateParameters($parameters)
    {
        if (!is_array($parameters)) {
            return false;
        }

        foreach ($parameters as $param) {
            if (!isset($param['id']) || !isset($param['elementUuid']) || 
                !isset($param['parameterName']) || !isset($param['parameterType'])) {
                return false;
            }
        }

        return true;
    }
}
